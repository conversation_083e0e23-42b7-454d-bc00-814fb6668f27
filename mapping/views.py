from django.contrib.auth.decorators import login_required
from django.contrib.gis.geos import GEOSGeometry
from django.shortcuts import render, redirect
from django.http import JsonResponse
import json
from django.views.decorators.csrf import csrf_protect, ensure_csrf_cookie
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import authentication_classes, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import authentication_classes, permission_classes
from django.http import HttpResponse, Http404
from .models import Hubs, Layers, Departments, Region, DepartmentType, UserRegionAccess
from django.db.models import Q
from django.conf import settings
import os

@login_required
def get_layer_polygons(request):
    layer_id = request.GET.get('layer_id')  # Получаем параметр layer_id из запроса
    ayer_id = request.GET.get('layer_id')
    region_id = request.GET.get('region_id')

    polygons = Hubs.objects.filter(
        layers_id=layer_id,
        department__region=region_id,
        department__status__in=[0, 1, 2, 3]
    ).values(
        'id',
        'polygon',
        'department__name',
        'department__lat',
        'department__lon',
        'department__status'
    )
    
    region= Region.objects.get(id = region_id)

    polygons_list = []
    for polygon in polygons:
        actual_geometry = GEOSGeometry(polygon['polygon'])  # Предполагая, что полигон хранится в формате HEX
        geometry_dict = json.loads(actual_geometry.geojson)  # Преобразование геометрии в JSON-совместимый словарь

        polygons_list.append({
            'id': polygon['id'],
            'geometry': geometry_dict,
            'departament': polygon['department__name'],
            'lat': polygon['department__lat'],
            'lon': polygon['department__lon'],
            'status': polygon['department__status'],
        })
    
    result={"polygons": polygons_list, "region_lat":region.lat, "region_lon":region.lon}

    return JsonResponse(result, safe=False)


@login_required
def layer_map(request):
    layers = Layers.objects.all()  # Получите список слоев из базы данных
    regions = Region.objects.all()

    user_region_access_list = UserRegionAccess.objects.filter(user=request.user)

    region_with_access = []
    for user_access in user_region_access_list:
        if user_access.has_full_access:
            # Если есть полный доступ, добавляем все категории
            region_with_access.extend(Region.objects.all())
            break  # Прерываем цикл, так как полный доступ есть к любой категории
        else:
            # Если нет полного доступа, добавляем только категорию, к которой есть доступ
            region_with_access.append(user_access.region)

    return render(request, 'layer_map.html', {'layers': layers, 'regions': region_with_access})


@login_required
def hubs_editor(request):
    """Страница для группового редактирования хабов - только для админов"""
    # Проверка прав доступа - только для staff пользователей
    if not request.user.is_staff:
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied("Доступ разрешен только администраторам")
    layers = Layers.objects.all()
    regions = Region.objects.all()

    # Фильтрация регионов по доступам пользователя (та же логика что в layer_map)
    user_region_access_list = UserRegionAccess.objects.filter(user=request.user)

    region_with_access = []
    for user_access in user_region_access_list:
        if user_access.has_full_access:
            region_with_access.extend(Region.objects.all())
            break
        else:
            region_with_access.append(user_access.region)

    context = {
        'layers': layers,
        'regions': region_with_access,
        'page_title': 'Групповое редактирование хабов'
    }

    return render(request, 'hubs_editor.html', context)


@login_required
def get_departments(request):
    region_id = request.GET.get('region_id')
    departmens = Departments.objects.filter(status__in=[1], region=region_id, lat__isnull=False, lon__isnull=False ).values('id', 'name', 'lat', 'lon', 'status')

    departmens_list = []
    for dep in departmens:
        departmens_list.append({
            'id': dep['id'],
            'name': dep['name'],
            'lat': dep['lat'],
            'lon': dep['lon'],
            'status': dep['status']
        })

    return JsonResponse(departmens_list, safe=False)


@login_required
def save_polygon(request):
    body = request.body
    str_json = body.decode('utf8').replace("'", '"')
    data = json.loads(str_json)

    departments = data['department']
    layer = data['layer']
    geometry = data['geometry']['coordinates'][0]

    polygon = GEOSGeometry(json.dumps({
        "type": "Polygon",
        "coordinates": [geometry]
    }))

    # Сохраните полигон и другие данные в базу данных
    polygon_data = Hubs(polygon=polygon, layers_id=layer, department_id=departments)
    polygon_data.save()
    return JsonResponse({"status": "Polygon saved successfully"}, safe=False)


@method_decorator(ensure_csrf_cookie, name='dispatch')
@authentication_classes([])
@permission_classes([])
class UpdateDepartment(APIView):
    def post(self, request, *args, **kwargs):
        lat = None
        lon = None
        body = request.body
        str_json = body.decode('utf8').replace("'", '"')
        data = json.loads(str_json)

        dep = data['dep-id']

        region_id = None
        dep_type = None
        class_field = None
        sub_department_id=None

        if 'class' in data:
            class_field=data.get('class', None)

        if 'region' in data:
            try:
                region_id = Region.objects.get(name=data['region'])
            except Region.DoesNotExist:
                print("REGION NOT FOUND")

        if 'type' in data:
            try:
                dep_type = DepartmentType.objects.get(code=data['type'])
            except DepartmentType.DoesNotExist:
                print("dep_type NOT FOUND")

        if 'sub_dep_id' in data:
            try:
                if data['sub_dep_id']==None:
                    sub_department_id=None
                else:
                    sub_department_id=Departments.objects.get(id=data['sub_dep_id'])
            except Departments.DoesNotExist:
                print("SUB Departments NOT FOUND")

        geo = data.get('geo', None)
        if geo:
            geo_data = geo.split(',')
            lat = geo_data[0].strip()
            lon = geo_data[1].strip()

        search_params = {'id': dep}

        update_values = {}
        possible_fields = ['name', 'code', 'post_code', 'new_post_code', 'status',  'location_name','dm_tindex']
        for field in possible_fields:
            if field in data:
                update_values[field] = data[field]

        if lat is not None:
            update_values['lat'] = lat
        if lon is not None:
            update_values['lon'] = lon

        if region_id is not None:
            update_values['region'] = region_id
        if dep_type is not None:
            update_values['type'] = dep_type

        if class_field is not None:
            update_values['class_field'] = class_field

        update_values['sub_department']=sub_department_id

        department, created = Departments.objects.update_or_create(defaults=update_values, **search_params)

        print(department, created)
        if created:
            res_message = "created"
        else:
            res_message = "updated"

        return JsonResponse({"status": "Department successfully " + res_message}, safe=False)
    
import mimetypes
def download_file(request, filename):
    file_path = os.path.join(settings.STATIC_ROOT, filename)

    if os.path.exists(file_path):
        # Get the MIME type for the file
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'

        with open(file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type=mime_type)
            response['Content-Disposition'] = 'attachment; filename="KAZ_POST2023-24RU.pdf"'
            return response
    else:
        raise Http404("File does not exist")

from django.contrib.auth import logout, authenticate, login
from django.http import HttpResponseRedirect
from django.urls import reverse

def CustomLogoutView(request):
    logout(request)
    return HttpResponseRedirect(request.META.get('HTTP_REFERER', reverse('layer_map')))


from .forms import MyAuthenticationForm

def login_view(request):
    if request.method == 'POST':
        form = MyAuthenticationForm(data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('layer_map')  # Замените 'home' на вашу страницу назначения
            else:
                form.add_error(None, 'Invalid username or password.')
    else:
        form = MyAuthenticationForm()
    
    return render(request, 'login2.html', {'form': form})