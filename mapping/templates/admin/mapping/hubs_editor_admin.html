{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">
<link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">

<style>
    /* Стили для интеграции карты в админку */
    .controls-section {
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .controls-section h3 {
        margin-top: 0;
        color: #333;
        font-size: 16px;
        font-weight: bold;
    }

    .form-row {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        align-items: end;
    }

    .form-group {
        flex: 1;
        min-width: 200px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
    }

    .btn-primary {
        background-color: #417690;
        color: white;
    }

    .btn-success {
        background-color: #5cb85c;
        color: white;
    }

    .btn-warning {
        background-color: #f0ad4e;
        color: white;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .help-text {
        margin-top: 15px;
        padding: 10px;
        background-color: #e7f3ff;
        border-left: 4px solid #2196F3;
        border-radius: 4px;
        font-size: 13px;
    }

    .help-text ul {
        margin: 10px 0 0 0;
        padding-left: 20px;
    }

    .help-text li {
        margin-bottom: 5px;
    }

    #map {
        height: 600px;
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 20px;
    }

    #hubs-status {
        margin-top: 15px;
        padding: 10px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 15px;
    }

    .checkbox-container input[type="checkbox"] {
        width: auto;
    }

    .checkbox-container label {
        margin: 0;
        font-weight: normal;
    }

    /* Стили для панели рисования Leaflet */
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптация для мобильных устройств */
    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }
        
        .form-group {
            min-width: auto;
        }
        
        #map {
            height: 400px;
        }
        
        .btn {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<h1>Групповое редактирование хабов</h1>

<div class="module">
    {% csrf_token %}
    
    <div class="controls-section">
        <h3>Параметры загрузки</h3>
        <div class="form-row">
            <div class="form-group">
                <label for="layer-select">Слой:</label>
                <select id="layer-select" class="form-control">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="region-select">Регион:</label>
                <select id="region-select" class="form-control">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group" style="flex: 0 0 auto;">
                <button id="load-hubs-button" type="button" class="btn btn-primary" disabled>Загрузить хабы</button>
            </div>
        </div>
        
        <div class="checkbox-container">
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <label for="show-polygons">Показывать только отделения с полигонами</label>
        </div>
    </div>

    <div class="controls-section">
        <h3>Действия с хабами</h3>
        <div>
            <button id="save-hubs-button" type="button" class="btn btn-success" disabled>Сохранить изменения</button>
            <button id="reset-hubs-button" type="button" class="btn btn-warning" disabled>Отменить изменения</button>
        </div>
        
        <div class="help-text">
            <strong>Инструкция:</strong>
            <ul>
                <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                <li>Используйте инструменты редактирования для изменения полигонов</li>
                <li>Нажмите "Сохранить изменения" для применения</li>
                <li>Синие полигоны - активные отделения, красные - неактивные</li>
            </ul>
        </div>
    </div>

    <div id="map"></div>
    
    <div id="hubs-status">
        <strong>Статус:</strong> Выберите слой и регион для начала работы
    </div>
</div>
{% endblock %}

{% block extrajs %}
{{ block.super }}
<script src="{% static 'map/js/leaflet.js' %}"></script>
<script src="{% static 'map/js/Control.Geocoder.js' %}"></script>
<script src="{% static 'map/js/leaflet.draw.js' %}"></script>

<script>
    $(document).ready(function() {
        var map = null;
        var drawnItems = null;
        var hubsData = [];
        var departments = [];
        var originalHubsData = [];
        var hasChanges = false;

        // Инициализация карты
        function initMap() {
            console.log('Initializing map...');
            
            if (map) {
                map.remove();
            }

            map = L.map('map').setView([43.257109, 76.946314], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            // Проверяем что Leaflet.Draw загружен
            if (typeof L.Control.Draw !== 'undefined') {
                console.log('Creating draw control...');

                var drawControl = new L.Control.Draw({
                    draw: {
                        polygon: {
                            allowIntersection: false,
                            showArea: true
                        },
                        polyline: false,
                        rectangle: false,
                        circle: false,
                        marker: false,
                        circlemarker: false
                    },
                    edit: {
                        featureGroup: drawnItems,
                        edit: true,
                        remove: true
                    }
                });

                map.addControl(drawControl);
                console.log('Draw control added to map');

                // Принудительно показываем панель инструментов
                setTimeout(function() {
                    var toolbar = document.querySelector('.leaflet-draw-toolbar');
                    if (toolbar) {
                        toolbar.style.display = 'block';
                        toolbar.style.visibility = 'visible';
                    }
                }, 200);

            } else {
                console.error('Leaflet.Draw not loaded!');
            }

            // Обработчик изменений
            map.on(L.Draw.Event.EDITED, function (event) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span>');
            });
        }

        // Обновление состояния кнопок
        function updateButtonStates() {
            var layerSelected = $('#layer-select').val();
            var regionSelected = $('#region-select').val();

            $('#load-hubs-button').prop('disabled', !layerSelected || !regionSelected);
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        // Загрузка отделений для региона
        function loadDepartmentsForRegion(selectedRegion, callback) {
            $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
                departments = data;
                if (callback) callback();
            });
        }

        // Отображение отделений на карте
        function displayDepartments() {
            departments.forEach(function (point) {
                var marker = L.marker([point.lat, point.lon]);
                marker.bindPopup(point.name);
                marker.addTo(map);
            });
        }

        // Загрузка хабов
        function loadHubs() {
            var selectedLayer = $('#layer-select').val();
            var selectedRegion = $('#region-select').val();
            var showPolygonsOnly = $('#show-polygons').is(':checked');

            if (!selectedLayer || !selectedRegion) {
                alert('Выберите слой и регион');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Загрузка...');

            // Очистка карты
            map.eachLayer(function (layer) {
                if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                    map.removeLayer(layer);
                }
            });
            drawnItems.clearLayers();

            // Загрузка отделений
            if (!showPolygonsOnly) {
                loadDepartmentsForRegion(selectedRegion, displayDepartments);
            }

            // Загрузка полигонов хабов
            var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

            $.getJSON(url, function (data) {
                hubsData = data.polygons;
                originalHubsData = JSON.parse(JSON.stringify(data.polygons)); // Глубокая копия

                data.polygons.forEach(function (polygon) {
                    var coords = polygon.geometry.coordinates[0];
                    var latLngs = coords.map(function (coord) {
                        return [coord[1], coord[0]];
                    });

                    var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                    var polygonLayer = L.polygon(latLngs, {
                        fillColor: fillColor,
                        interactive: true,
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.3
                    });

                    polygonLayer.hubId = polygon.id;
                    polygonLayer.originalData = polygon;

                    polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                        permanent: false,
                        direction: 'auto',
                    });

                    drawnItems.addLayer(polygonLayer);
                });

                if (data.region_lat && data.region_lon) {
                    map.setView([data.region_lat, data.region_lon], 10);
                }

                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Загружено хабов: ' + data.polygons.length);
            }).fail(function() {
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: red;">Ошибка загрузки</span>');
            });
        }

        // Сохранение изменений
        function saveHubs() {
            if (!hasChanges) {
                alert('Нет изменений для сохранения');
                return;
            }

            var changedHubs = [];

            drawnItems.eachLayer(function(layer) {
                if (layer instanceof L.Polygon && layer.hubId) {
                    var geoJson = layer.toGeoJSON();
                    changedHubs.push({
                        id: layer.hubId,
                        geometry: geoJson.geometry
                    });
                }
            });

            if (changedHubs.length === 0) {
                alert('Нет полигонов для сохранения');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Сохранение...');

            // TODO: Здесь будет AJAX запрос для массового сохранения
            console.log('Сохраняем хабы:', changedHubs);

            // Временная имитация сохранения
            setTimeout(function() {
                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: green;">Сохранено хабов: ' + changedHubs.length + '</span>');
                alert('Изменения сохранены! (пока только в консоли)');
            }, 1000);
        }

        // Отмена изменений
        function resetHubs() {
            if (!hasChanges) {
                return;
            }

            if (!confirm('Отменить все несохраненные изменения?')) {
                return;
            }

            // Очистка и перезагрузка оригинальных данных
            drawnItems.clearLayers();

            originalHubsData.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                var polygonLayer = L.polygon(latLngs, {
                    fillColor: fillColor,
                    interactive: true,
                    weight: 2,
                    opacity: 0.8,
                    fillOpacity: 0.3
                });

                polygonLayer.hubId = polygon.id;
                polygonLayer.originalData = polygon;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });

                drawnItems.addLayer(polygonLayer);
            });

            hasChanges = false;
            updateButtonStates();
            $('#hubs-status').html('<strong>Статус:</strong> Изменения отменены');
        }

        // Обработчики событий
        $('#layer-select, #region-select').on('change', updateButtonStates);
        $('#load-hubs-button').on('click', loadHubs);
        $('#save-hubs-button').on('click', saveHubs);
        $('#reset-hubs-button').on('click', resetHubs);

        // Инициализация
        initMap();
        updateButtonStates();
    });
</script>
{% endblock %}
