{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; Групповое редактирование хабов
</div>
{% endblock %}

{% block nav-sidebar %}
{% include "admin/nav_sidebar.html" %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">

<style>
    /* Переопределяем стили админки для карты */
    #content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    /* Принудительно переопределяем стили админки для селектов */
    .hubs-editor-container select {
        background: white !important;
        color: #333 !important;
        border: 1px solid #ccc !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
        height: auto !important;
        line-height: normal !important;
        font-weight: normal !important;
    }

    .hubs-editor-container select option {
        color: #333 !important;
        background: white !important;
    }

    /* Исправляем стили для кнопок */
    .hubs-editor-container button {
        font-size: 14px !important;
        font-weight: normal !important;
        text-transform: none !important;
    }

    /* Контейнер для карты */
    .hubs-editor-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    /* Панель управления */
    .controls-panel {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 20px;
    }

    .controls-row {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }

    .control-group {
        flex: 1;
        min-width: 200px;
    }

    .control-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .control-group select {
        width: 100%;
        min-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
        color: #495057 !important;
        font-weight: normal !important;
        height: auto !important;
        line-height: 1.5;
    }

    .control-group select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    .control-group select option {
        color: #495057 !important;
        background-color: white !important;
        padding: 8px;
    }

    /* Кнопки */
    .btn-custom {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        transition: all 0.2s;
    }

    .btn-primary-custom {
        background-color: #007bff;
        color: white;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-success-custom {
        background-color: #28a745;
        color: white;
    }

    .btn-success-custom:hover:not(:disabled) {
        background-color: #1e7e34;
    }

    .btn-warning-custom {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-warning-custom:hover:not(:disabled) {
        background-color: #e0a800;
    }

    .btn-custom:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Чекбокс */
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto !important;
        margin: 0 !important;
        height: auto !important;
    }

    .checkbox-group label {
        margin: 0 !important;
        font-weight: normal !important;
        cursor: pointer !important;
        color: #333 !important;
        font-size: 14px !important;
    }

    /* Карта */
    .map-container {
        position: relative;
        height: 600px !important;
        background: #f8f9fa;
        border: 1px solid #ddd;
    }

    #map {
        height: 600px !important;
        width: 100% !important;
        border: none;
        position: relative !important;
        z-index: 1;
    }

    /* Принудительно показываем карту */
    .leaflet-container {
        height: 600px !important;
        width: 100% !important;
    }

    /* Статус */
    .status-panel {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 15px 20px;
        font-size: 14px;
    }

    /* Инструкции */
    .instructions {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
    }

    .instructions h4 {
        margin: 0 0 10px 0;
        color: #0056b3;
        font-size: 14px;
        font-weight: 600;
    }

    .instructions ul {
        margin: 0;
        padding-left: 20px;
    }

    .instructions li {
        margin-bottom: 5px;
        font-size: 13px;
        color: #495057;
    }

    /* Исправляем конфликты с админкой */
    .hubs-editor-container * {
        box-sizing: border-box;
    }

    /* Leaflet стили */
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
        }

        .control-group {
            min-width: auto;
        }

        .map-container {
            height: 400px;
        }

        .btn-custom {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="hubs-editor-container">
    <p style="color: red; font-size: 18px; font-weight: bold; background: yellow; padding: 10px;">
        ТЕСТ: Если вы видите этот текст, HTML рендерится правильно!
    </p>
    {% csrf_token %}

    <!-- Панель управления -->
    <div class="controls-panel">
        <div class="controls-row">
            <div class="control-group">
                <label for="layer-select">Слой:</label>
                <select id="layer-select">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <label for="region-select">Регион:</label>
                <select id="region-select">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <button id="load-hubs-button" type="button" class="btn-custom btn-primary-custom" disabled>Загрузить хабы</button>
            </div>
        </div>

        <div class="checkbox-group">
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <label for="show-polygons">Показывать только полигоны</label>
        </div>

        <div class="controls-row" style="margin-top: 15px;">
            <div class="control-group">
                <button id="save-hubs-button" type="button" class="btn-custom btn-success-custom" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn-custom btn-warning-custom" disabled>Отменить изменения</button>
            </div>
        </div>

        <div class="instructions">
            <h4>Инструкция по использованию:</h4>
            <ul>
                <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                <li>Используйте инструменты редактирования на карте для изменения полигонов</li>
                <li>Нажмите "Сохранить изменения" для применения</li>
                <li>Синие полигоны - активные отделения, красные - неактивные</li>
            </ul>
        </div>
    </div>

    <!-- Карта -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Статус -->
    <div class="status-panel">
        <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
{{ block.super }}

<!-- Простая проверка -->
<script>
console.log('=== SCRIPT BLOCK STARTED ===');
console.log('Document ready state:', document.readyState);
console.log('Static URL test:', '{% static "map/js/leaflet.js" %}');
console.log('Hubs editor URL:', '{% static "map/js/hubs_editor.js" %}');
</script>

<!-- Leaflet основной файл -->
<script src="{% static 'map/js/leaflet.js' %}" onload="console.log('Leaflet.js loaded')" onerror="console.error('Failed to load Leaflet.js')"></script>
<!-- Leaflet Draw -->
<script src="{% static 'map/js/leaflet.draw.js' %}" onload="console.log('Leaflet.draw.js loaded')" onerror="console.error('Failed to load Leaflet.draw.js')"></script>
<!-- Geocoder -->
<script src="{% static 'map/js/Control.Geocoder.js' %}" onload="console.log('Control.Geocoder.js loaded')" onerror="console.error('Failed to load Control.Geocoder.js')"></script>
<!-- Простейшая проверка -->
<script>
alert('JavaScript работает!');
console.log('=== INLINE SCRIPT WORKS ===');
</script>

<!-- Hubs Editor -->
<script src="{% static 'map/js/hubs_editor.js' %}" onload="console.log('Hubs editor loaded')" onerror="console.error('Failed to load hubs editor')"></script>

{% endblock %}
