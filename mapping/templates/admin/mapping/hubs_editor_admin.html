{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; Групповое редактирование хабов
</div>
{% endblock %}

{% block nav-sidebar %}
{% include "admin/nav_sidebar.html" %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">

<style>
    /* Переопределяем стили админки для карты */
    #content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    /* Контейнер для карты */
    .hubs-editor-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    /* Панель управления */
    .controls-panel {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 20px;
    }

    .controls-row {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }

    .control-group {
        flex: 1;
        min-width: 200px;
    }

    .control-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .control-group select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
    }

    .control-group select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    /* Кнопки */
    .btn-custom {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        transition: all 0.2s;
    }

    .btn-primary-custom {
        background-color: #007bff;
        color: white;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-success-custom {
        background-color: #28a745;
        color: white;
    }

    .btn-success-custom:hover:not(:disabled) {
        background-color: #1e7e34;
    }

    .btn-warning-custom {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-warning-custom:hover:not(:disabled) {
        background-color: #e0a800;
    }

    .btn-custom:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Чекбокс */
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }

    .checkbox-group label {
        margin: 0;
        font-weight: normal;
        cursor: pointer;
    }

    /* Карта */
    .map-container {
        position: relative;
        height: 600px;
        background: #f8f9fa;
    }

    #map {
        height: 100%;
        width: 100%;
        border: none;
    }

    /* Статус */
    .status-panel {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 15px 20px;
        font-size: 14px;
    }

    /* Инструкции */
    .instructions {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
    }

    .instructions h4 {
        margin: 0 0 10px 0;
        color: #0056b3;
        font-size: 14px;
        font-weight: 600;
    }

    .instructions ul {
        margin: 0;
        padding-left: 20px;
    }

    .instructions li {
        margin-bottom: 5px;
        font-size: 13px;
        color: #495057;
    }

    /* Исправляем конфликты с админкой */
    .hubs-editor-container * {
        box-sizing: border-box;
    }

    /* Leaflet стили */
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
        }

        .control-group {
            min-width: auto;
        }

        .map-container {
            height: 400px;
        }

        .btn-custom {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="hubs-editor-container">
    {% csrf_token %}

    <!-- Панель управления -->
    <div class="controls-panel">
        <div class="controls-row">
            <div class="control-group">
                <label for="layer-select">Слой:</label>
                <select id="layer-select">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <label for="region-select">Регион:</label>
                <select id="region-select">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <button id="load-hubs-button" type="button" class="btn-custom btn-primary-custom" disabled>Загрузить хабы</button>
            </div>
        </div>

        <div class="checkbox-group">
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <label for="show-polygons">Показывать только полигоны</label>
        </div>

        <div class="controls-row" style="margin-top: 15px;">
            <div class="control-group">
                <button id="save-hubs-button" type="button" class="btn-custom btn-success-custom" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn-custom btn-warning-custom" disabled>Отменить изменения</button>
            </div>
        </div>

        <div class="instructions">
            <h4>Инструкция по использованию:</h4>
            <ul>
                <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                <li>Используйте инструменты редактирования на карте для изменения полигонов</li>
                <li>Нажмите "Сохранить изменения" для применения</li>
                <li>Синие полигоны - активные отделения, красные - неактивные</li>
            </ul>
        </div>
    </div>

    <!-- Карта -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Статус -->
    <div class="status-panel">
        <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
{{ block.super }}
<script src="{% static 'map/js/leaflet.js' %}"></script>
<script src="{% static 'map/js/Control.Geocoder.js' %}"></script>
<script src="{% static 'map/js/leaflet.draw.js' %}"></script>

<script>
    $(document).ready(function() {
        var map = null;
        var drawnItems = null;
        var hubsData = [];
        var departments = [];
        var originalHubsData = [];
        var hasChanges = false;

        // Инициализация карты
        function initMap() {
            console.log('Initializing map...');
            
            if (map) {
                map.remove();
            }

            map = L.map('map').setView([43.257109, 76.946314], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            // Проверяем что Leaflet.Draw загружен
            if (typeof L.Control.Draw !== 'undefined') {
                console.log('Creating draw control...');

                var drawControl = new L.Control.Draw({
                    draw: {
                        polygon: {
                            allowIntersection: false,
                            showArea: true
                        },
                        polyline: false,
                        rectangle: false,
                        circle: false,
                        marker: false,
                        circlemarker: false
                    },
                    edit: {
                        featureGroup: drawnItems,
                        edit: true,
                        remove: true
                    }
                });

                map.addControl(drawControl);
                console.log('Draw control added to map');

                // Принудительно показываем панель инструментов
                setTimeout(function() {
                    var toolbar = document.querySelector('.leaflet-draw-toolbar');
                    if (toolbar) {
                        toolbar.style.display = 'block';
                        toolbar.style.visibility = 'visible';
                    }
                }, 200);

            } else {
                console.error('Leaflet.Draw not loaded!');
            }

            // Обработчик изменений
            map.on(L.Draw.Event.EDITED, function (event) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span>');
            });
        }

        // Обновление состояния кнопок
        function updateButtonStates() {
            var layerSelected = $('#layer-select').val();
            var regionSelected = $('#region-select').val();

            $('#load-hubs-button').prop('disabled', !layerSelected || !regionSelected);
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        // Загрузка отделений для региона
        function loadDepartmentsForRegion(selectedRegion, callback) {
            $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
                departments = data;
                if (callback) callback();
            });
        }

        // Отображение отделений на карте
        function displayDepartments() {
            departments.forEach(function (point) {
                var marker = L.marker([point.lat, point.lon]);
                marker.bindPopup(point.name);
                marker.addTo(map);
            });
        }

        // Загрузка хабов
        function loadHubs() {
            var selectedLayer = $('#layer-select').val();
            var selectedRegion = $('#region-select').val();
            var showPolygonsOnly = $('#show-polygons').is(':checked');

            if (!selectedLayer || !selectedRegion) {
                alert('Выберите слой и регион');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Загрузка...');

            // Очистка карты
            map.eachLayer(function (layer) {
                if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                    map.removeLayer(layer);
                }
            });
            drawnItems.clearLayers();

            // Загрузка отделений
            if (!showPolygonsOnly) {
                loadDepartmentsForRegion(selectedRegion, displayDepartments);
            }

            // Загрузка полигонов хабов
            var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

            $.getJSON(url, function (data) {
                hubsData = data.polygons;
                originalHubsData = JSON.parse(JSON.stringify(data.polygons)); // Глубокая копия

                data.polygons.forEach(function (polygon) {
                    var coords = polygon.geometry.coordinates[0];
                    var latLngs = coords.map(function (coord) {
                        return [coord[1], coord[0]];
                    });

                    var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                    var polygonLayer = L.polygon(latLngs, {
                        fillColor: fillColor,
                        interactive: true,
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.3
                    });

                    polygonLayer.hubId = polygon.id;
                    polygonLayer.originalData = polygon;

                    polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                        permanent: false,
                        direction: 'auto',
                    });

                    drawnItems.addLayer(polygonLayer);
                });

                if (data.region_lat && data.region_lon) {
                    map.setView([data.region_lat, data.region_lon], 10);
                }

                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Загружено хабов: ' + data.polygons.length);
            }).fail(function() {
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: red;">Ошибка загрузки</span>');
            });
        }

        // Сохранение изменений
        function saveHubs() {
            if (!hasChanges) {
                alert('Нет изменений для сохранения');
                return;
            }

            var changedHubs = [];

            drawnItems.eachLayer(function(layer) {
                if (layer instanceof L.Polygon && layer.hubId) {
                    var geoJson = layer.toGeoJSON();
                    changedHubs.push({
                        id: layer.hubId,
                        geometry: geoJson.geometry
                    });
                }
            });

            if (changedHubs.length === 0) {
                alert('Нет полигонов для сохранения');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Сохранение...');

            // TODO: Здесь будет AJAX запрос для массового сохранения
            console.log('Сохраняем хабы:', changedHubs);

            // Временная имитация сохранения
            setTimeout(function() {
                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: green;">Сохранено хабов: ' + changedHubs.length + '</span>');
                alert('Изменения сохранены! (пока только в консоли)');
            }, 1000);
        }

        // Отмена изменений
        function resetHubs() {
            if (!hasChanges) {
                return;
            }

            if (!confirm('Отменить все несохраненные изменения?')) {
                return;
            }

            // Очистка и перезагрузка оригинальных данных
            drawnItems.clearLayers();

            originalHubsData.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                var polygonLayer = L.polygon(latLngs, {
                    fillColor: fillColor,
                    interactive: true,
                    weight: 2,
                    opacity: 0.8,
                    fillOpacity: 0.3
                });

                polygonLayer.hubId = polygon.id;
                polygonLayer.originalData = polygon;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });

                drawnItems.addLayer(polygonLayer);
            });

            hasChanges = false;
            updateButtonStates();
            $('#hubs-status').html('<strong>Статус:</strong> Изменения отменены');
        }

        // Обработчики событий
        $('#layer-select, #region-select').on('change', updateButtonStates);
        $('#load-hubs-button').on('click', loadHubs);
        $('#save-hubs-button').on('click', saveHubs);
        $('#reset-hubs-button').on('click', resetHubs);

        // Инициализация
        initMap();
        updateButtonStates();
    });
</script>
{% endblock %}
