{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; Групповое редактирование хабов
</div>
{% endblock %}

{% block nav-sidebar %}
{% include "admin/nav_sidebar.html" %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">



<!-- Подключаем jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Подключаем Bootstrap для модальных окон -->
<link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">
<script src="{% static 'map/js/bootstrap.min.js' %}"></script>

<!-- Подключаем JS файлы -->
<script src="{% static 'map/js/leaflet.js' %}"></script>
<script src="{% static 'map/js/leaflet.draw.js' %}"></script>
<script src="{% static 'map/js/turf.min.js' %}"></script>
<script src="{% static 'map/js/Control.Geocoder.js' %}"></script>

<script>
$(document).ready(function () {
    var map = L.map('map').setView([{{regions.0.lat }}, {{regions.0.lon}}], 12);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
    }).addTo(map);

    var departments = null;
    var selectedPolygon = null;

    // Загрузка отделений для региона
    function loadDepartmentsForRegion(selectedRegion, callback) {
        $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
            if (data.length > 0) {
                var firstDepartment = data[0];
                console.log('firstDepartment: ', firstDepartment)
                var lat = firstDepartment.lat;
                var lon = firstDepartment.lon;
                console.log('lat: ', firstDepartment.lat)
            }
            departments = data;
            callback();
        });
    }

    // Функция для отображения отделений на карте
    function displayDepartments() {
        departments.forEach(function (point) {
            var lat = point.lat;
            var lon = point.lon;
            var name = point.name;

            // Создание маркера для точки
            var marker = L.marker([lat, lon]);

            // Добавление всплывающей подсказки с именем точки
            marker.bindPopup(name);

            marker.addTo(map);
        });
    }

    var drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    var drawControl = new L.Control.Draw({
        draw: {
            polygon: true,
            polyline: false,
            rectangle: false,
            circle: false,
            marker: false,
        },
        edit: {
            featureGroup: drawnItems,
            edit: true,
        },
    });
    map.addControl(drawControl);
    map.attributionControl.setPrefix('Kazpost Digital, 2024');

    // Обработчики селектов
    function updateButtonState() {
        var layerId = $('#layer-select').val();
        var regionId = $('#region-select').val();
        $('#load-hubs-button').prop('disabled', !layerId || !regionId);
    }

    $('#layer-select, #region-select').on('change', updateButtonState);
    updateButtonState(); // Инициализация

    // Обработчик кнопки загрузки хабов
    $('#load-hubs-button').on('click', function() {
        var selectedLayer = $('#layer-select').val();
        var selectedRegion = $('#region-select').val();
        var showPolygonsOnly = $('#show-polygons').is(':checked');

        if (!selectedLayer || !selectedRegion) {
            alert('Выберите слой и регион');
            return;
        }

        // Очистка карты от предыдущих полигонов и маркеров
        map.eachLayer(function (layer) {
            if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                map.removeLayer(layer);
            }
        });

        // Загрузка данных об отделениях и отображение их на карте
        if (!showPolygonsOnly){
            loadDepartmentsForRegion(selectedRegion, displayDepartments);
        }

        // Загрузка и отображение полигонов, связанных с выбранным слоем
        var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

        $.getJSON(url, function (data) {
            console.log("полигоны по слою", data)

            // Сбрасываем отслеживание изменений при загрузке новых данных
            changedPolygons.clear();
            deletedPolygons.clear();
            updateStatus();

            data.polygons.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = 'blue'; // Цвет по умолчанию
                // Изменение цвета в зависимости от статуса
                if (polygon.status !== 1) {
                    fillColor = 'red'; // Или любой другой цвет для статуса, отличного от 1
                }

                var polygonLayer = L.polygon(latLngs, {fillColor: fillColor,
                    interactive: true,
                });

                // Добавляем ID хаба и название отделения к полигону для отслеживания
                polygonLayer.hubId = polygon.id;
                polygonLayer.departmentName = polygon.departament;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });  // Всплывающая подсказка

                polygonLayer.on('mouseover', function () {
                    if (selectedPolygon !== polygonLayer) {
                        polygonLayer.setStyle({fillColor: 'green'});  // Изменение цвета при наведении
                    }
                });

                polygonLayer.on('mouseout', function () {
                    if (selectedPolygon !== polygonLayer) {
                        if (polygon.status !== 1) {
                            polygonLayer.setStyle({fillColor: 'red'}); // Или любой другой цвет для статуса, отличного от 1
                        } else {
                            polygonLayer.setStyle({fillColor: 'blue'});
                        }
                        // Возвращение исходного цвета
                    }
                });

                drawnItems.addLayer(polygonLayer);  // Добавляем в группу редактируемых полигонов

                polygonLayer.addTo(map);

                // Добавление маркера для координат департамента
                var departmentMarker = L.marker([polygon.lat, polygon.lon]).addTo(map);
                departmentMarker.bindPopup(polygon.departament).openPopup();
            });
            console.log("region_lat ",data.region_lat)
            map.setView([data.region_lat, data.region_lon], 12)
        });
    });

    var changedPolygons = new Map(); // Отслеживаем измененные полигоны: ID -> название
    var deletedPolygons = new Map(); // Отслеживаем удаленные полигоны: ID -> название

    // Функция обновления статуса
    function updateStatus() {
        var totalChanges = changedPolygons.size + deletedPolygons.size;
        var statusText = '';

        if (totalChanges > 0) {
            statusText = '<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span><br>';

            if (changedPolygons.size > 0) {
                var changedNames = Array.from(changedPolygons.values()).join(', ');
                statusText += '• Отредактировано: ' + changedNames + '<br>';
            }

            if (deletedPolygons.size > 0) {
                var deletedNames = Array.from(deletedPolygons.values()).join(', ');
                statusText += '• Удалено: ' + deletedNames;
            }

            $('#save-hubs-button, #reset-hubs-button').prop('disabled', false);
        } else {
            statusText = '<strong>Статус:</strong> Нет изменений';
            $('#save-hubs-button, #reset-hubs-button').prop('disabled', true);
        }

        $('#hubs-status').html(statusText);
    }

    // Обработчики редактирования полигонов
    map.on(L.Draw.Event.EDITED, function (event) {
        var layers = event.layers;

        layers.eachLayer(function (layer) {
            if (layer.hubId && layer.departmentName) {
                changedPolygons.set(layer.hubId, layer.departmentName);
                // Меняем цвет отредактированного полигона
                layer.setStyle({fillColor: 'orange', fillOpacity: 0.5});
                console.log('Отредактирован хаб:', layer.departmentName, '(ID:', layer.hubId + ')');
            }
        });

        updateStatus();
    });

    // Обработчик удаления полигонов
    map.on(L.Draw.Event.DELETED, function (event) {
        var layers = event.layers;

        layers.eachLayer(function (layer) {
            if (layer.hubId && layer.departmentName) {
                deletedPolygons.set(layer.hubId, layer.departmentName);
                changedPolygons.delete(layer.hubId); // Убираем из измененных если был там
                console.log('Удален хаб:', layer.departmentName, '(ID:', layer.hubId + ')');
            }
        });

        updateStatus();
    });

    // Обработчик кнопки "Сохранить изменения"
    $('#save-hubs-button').on('click', function() {
        var totalChanges = changedPolygons.size + deletedPolygons.size;

        if (totalChanges === 0) {
            alert('Нет изменений для сохранения');
            return;
        }

        var confirmMessage = 'Сохранить изменения?\n\n';
        if (changedPolygons.size > 0) {
            var changedNames = Array.from(changedPolygons.values()).join(', ');
            confirmMessage += '• Отредактированные: ' + changedNames + '\n';
        }
        if (deletedPolygons.size > 0) {
            var deletedNames = Array.from(deletedPolygons.values()).join(', ');
            confirmMessage += '• Удаленные: ' + deletedNames + '\n';
        }

        if (confirm(confirmMessage)) {
            console.log('Измененные хабы:', Array.from(changedPolygons));
            console.log('Удаленные хабы:', Array.from(deletedPolygons));

            // TODO: Здесь будет AJAX запрос для массового сохранения
            alert('Сохранение выполнено!\n(пока только в консоли)');

            // Сбрасываем отслеживание изменений
            changedPolygons.clear();
            deletedPolygons.clear();
            updateStatus();
        }
    });

    // Обработчик кнопки "Отменить изменения"
    $('#reset-hubs-button').on('click', function() {
        var totalChanges = changedPolygons.size + deletedPolygons.size;

        if (totalChanges === 0) {
            return;
        }

        var resetMessage = 'Отменить все несохраненные изменения?\n\n';
        if (changedPolygons.size > 0) {
            var changedNames = Array.from(changedPolygons.values()).join(', ');
            resetMessage += '• Отредактированные: ' + changedNames + '\n';
        }
        if (deletedPolygons.size > 0) {
            var deletedNames = Array.from(deletedPolygons.values()).join(', ');
            resetMessage += '• Удаленные: ' + deletedNames;
        }

        if (confirm(resetMessage)) {
            // Сбрасываем отслеживание изменений
            changedPolygons.clear();
            deletedPolygons.clear();

            // Перезагружаем хабы
            $('#load-hubs-button').click();
        }
    });
});
</script>

<style>
    /* Переопределяем стили админки для карты */
    #content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    /* Принудительно переопределяем стили админки для селектов */
    .hubs-editor-container select {
        background: white !important;
        color: #333 !important;
        border: 1px solid #ccc !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
        height: auto !important;
        line-height: normal !important;
        font-weight: normal !important;
    }

    .hubs-editor-container select option {
        color: #333 !important;
        background: white !important;
    }

    /* Исправляем стили для кнопок */
    .hubs-editor-container button {
        font-size: 14px !important;
        font-weight: normal !important;
        text-transform: none !important;
    }

    /* Контейнер для карты */
    .hubs-editor-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    /* Панель управления */
    .controls-panel {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 20px;
    }

    .controls-row {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }

    .control-group {
        flex: 1;
        min-width: 200px;
    }

    .control-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .control-group select {
        width: 100%;
        min-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
        color: #495057 !important;
        font-weight: normal !important;
        height: auto !important;
        line-height: 1.5;
    }

    .control-group select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    .control-group select option {
        color: #495057 !important;
        background-color: white !important;
        padding: 8px;
    }

    /* Кнопки */
    .btn-custom {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        transition: all 0.2s;
    }

    .btn-primary-custom {
        background-color: #007bff;
        color: white;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-success-custom {
        background-color: #28a745;
        color: white;
    }

    .btn-success-custom:hover:not(:disabled) {
        background-color: #1e7e34;
    }

    .btn-warning-custom {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-warning-custom:hover:not(:disabled) {
        background-color: #e0a800;
    }

    .btn-custom:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Чекбокс */
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto !important;
        margin: 0 !important;
        height: auto !important;
    }

    .checkbox-group label {
        margin: 0 !important;
        font-weight: normal !important;
        cursor: pointer !important;
        color: #333 !important;
        font-size: 14px !important;
    }

    /* Карта */
    .map-container {
        position: relative;
        height: 600px !important;
        background: #f8f9fa;
        border: 1px solid #ddd;
    }

    #map {
        height: 600px !important;
        width: 100% !important;
        border: none;
        position: relative !important;
        z-index: 1;
    }

    /* Принудительно показываем карту */
    .leaflet-container {
        height: 600px !important;
        width: 100% !important;
    }

    /* Статус */
    .status-panel {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 15px 20px;
        font-size: 14px;
    }

    /* Инструкции */
    .instructions {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
    }

    .instructions h4 {
        margin: 0 0 10px 0;
        color: #0056b3;
        font-size: 14px;
        font-weight: 600;
    }

    .instructions ul {
        margin: 0;
        padding-left: 20px;
    }

    .instructions li {
        margin-bottom: 5px;
        font-size: 13px;
        color: #495057;
    }

    /* Исправляем конфликты с админкой */
    .hubs-editor-container * {
        box-sizing: border-box;
    }

    /* Leaflet стили */
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
        }

        .control-group {
            min-width: auto;
        }

        .map-container {
            height: 400px;
        }

        .btn-custom {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="hubs-editor-container">

    {% csrf_token %}

    <!-- Панель управления -->
    <div class="controls-panel">
        <div class="controls-row">
            <div class="control-group">
                <label for="layer-select">Слой:</label>
                <select id="layer-select">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <label for="region-select">Регион:</label>
                <select id="region-select">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <button id="load-hubs-button" type="button" class="btn-custom btn-primary-custom" disabled>Показать полигоны</button>
            </div>
        </div>

        <div class="checkbox-group">
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <label for="show-polygons">Показывать только полигоны</label>
        </div>

        <div class="controls-row" style="margin-top: 15px;">
            <div class="control-group">
                <button id="save-hubs-button" type="button" class="btn-custom btn-success-custom" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn-custom btn-warning-custom" disabled>Отменить изменения</button>
            </div>
        </div>


    </div>

    <!-- Карта -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Статус -->
    <div class="status-panel">
        <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
    </div>
</div>
{% endblock %}


