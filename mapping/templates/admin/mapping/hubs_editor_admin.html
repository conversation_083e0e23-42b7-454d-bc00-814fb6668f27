{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">
<link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">

<style>
    /* Стили для интеграции с админкой Django */
    #content-main {
        margin: 0;
        padding: 20px;
    }
    
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптация под стили админки */
    .controls-section {
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .controls-section h3 {
        margin-top: 0;
        color: #333;
        font-size: 16px;
        font-weight: bold;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
    }

    .btn-primary {
        background-color: #417690;
        color: white;
    }

    .btn-success {
        background-color: #5cb85c;
        color: white;
    }

    .btn-warning {
        background-color: #f0ad4e;
        color: white;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .help-text {
        margin-top: 15px;
        padding: 10px;
        background-color: #e7f3ff;
        border-left: 4px solid #2196F3;
        border-radius: 4px;
    }

    .help-text ul {
        margin: 10px 0 0 0;
        padding-left: 20px;
    }

    .help-text li {
        margin-bottom: 5px;
    }

    #map {
        height: 600px;
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 20px;
    }

    #hubs-status {
        margin-top: 15px;
        padding: 10px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    /* Адаптация для мобильных устройств */
    @media (max-width: 768px) {
        .controls-section {
            padding: 10px;
        }
        
        #map {
            height: 400px;
        }
        
        .btn {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module">
        <h1>Групповое редактирование хабов</h1>
        
        {% csrf_token %}
        
        <div class="controls-section">
            <h3>Параметры загрузки</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div class="form-group" style="flex: 1; min-width: 200px;">
                    <label for="layer-select">Слой:</label>
                    <select id="layer-select" class="form-control">
                        <option value="">Выберите слой</option>
                        {% for layer in layers %}
                        <option value="{{ layer.id }}">{{ layer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group" style="flex: 1; min-width: 200px;">
                    <label for="region-select">Регион:</label>
                    <select id="region-select" class="form-control">
                        <option value="">Выберите регион</option>
                        {% for region in regions %}
                        <option value="{{ region.id }}">{{ region.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group" style="flex: 0 0 auto; align-self: end;">
                    <button id="load-hubs-button" type="button" class="btn btn-primary" disabled>Загрузить хабы</button>
                </div>
            </div>
            
            <div class="help-text">
                <strong>Только полигоны:</strong>
                <input type="checkbox" id="only-polygons-checkbox" checked>
                <label for="only-polygons-checkbox" style="display: inline; margin-left: 5px; font-weight: normal;">Показывать только отделения с полигонами</label>
            </div>
        </div>

        <div class="controls-section">
            <h3>Действия с хабами</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button id="save-hubs-button" type="button" class="btn btn-success" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn btn-warning" disabled>Отменить изменения</button>
            </div>
            
            <div class="help-text">
                <strong>Инструкция:</strong>
                <ul>
                    <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                    <li>Используйте инструменты редактирования для изменения полигонов</li>
                    <li>Нажмите "Сохранить изменения" для применения</li>
                    <li>Синие полигоны - активные отделения, красные - неактивные</li>
                </ul>
            </div>
        </div>

        <div id="map"></div>
        
        <div id="hubs-status">
            <strong>Статус:</strong> Выберите слой и регион для начала работы
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
{{ block.super }}
<script src="{% static 'map/js/leaflet.js' %}"></script>
<script src="{% static 'map/js/Control.Geocoder.js' %}"></script>
<script src="{% static 'map/js/leaflet.draw.js' %}"></script>

<script>
    $(document).ready(function() {
        let map;
        let drawnItems;
        let drawControl;
        let hubsData = [];
        let originalHubsData = [];
        let hasChanges = false;

        function initMap() {
            // Инициализация карты
            map = L.map('map').setView([48.0196, 66.9237], 6);

            // Добавление тайлов
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Инициализация слоя для рисования
            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            // Настройка контролов рисования
            drawControl = new L.Control.Draw({
                edit: {
                    featureGroup: drawnItems,
                    remove: true
                },
                draw: {
                    polygon: true,
                    polyline: false,
                    rectangle: false,
                    circle: false,
                    marker: false,
                    circlemarker: false
                }
            });
            map.addControl(drawControl);

            // Обработчики событий карты
            map.on(L.Draw.Event.CREATED, function(e) {
                const layer = e.layer;
                drawnItems.addLayer(layer);
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Добавлен новый полигон');
            });

            map.on(L.Draw.Event.EDITED, function(e) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Полигоны изменены');
            });

            map.on(L.Draw.Event.DELETED, function(e) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Полигоны удалены');
            });
        }

        function updateButtonStates() {
            const layerSelected = $('#layer-select').val();
            const regionSelected = $('#region-select').val();
            
            $('#load-hubs-button').prop('disabled', !layerSelected || !regionSelected);
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        function loadHubs() {
            const layerId = $('#layer-select').val();
            const regionId = $('#region-select').val();
            const onlyPolygons = $('#only-polygons-checkbox').is(':checked');

            if (!layerId || !regionId) {
                alert('Пожалуйста, выберите слой и регион');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Загрузка хабов...');

            // Очистка карты
            drawnItems.clearLayers();

            $.ajax({
                url: '/get_layer_polygons/',
                method: 'GET',
                data: {
                    layer_id: layerId,
                    region_id: regionId,
                    only_polygons: onlyPolygons ? 1 : 0
                },
                success: function(data) {
                    hubsData = data;
                    originalHubsData = JSON.parse(JSON.stringify(data)); // Глубокая копия

                    let loadedCount = 0;
                    data.forEach(function(hub) {
                        if (hub.polygon) {
                            try {
                                const polygon = L.geoJSON(JSON.parse(hub.polygon), {
                                    style: function(feature) {
                                        return {
                                            color: hub.department__status === 1 ? '#0066cc' : '#cc0000',
                                            weight: 2,
                                            opacity: 0.8,
                                            fillOpacity: 0.3
                                        };
                                    }
                                });

                                polygon.bindPopup(`
                                    <strong>${hub.department__name}</strong><br>
                                    Статус: ${hub.department__status === 1 ? 'Активное' : 'Неактивное'}<br>
                                    Координаты: ${hub.department__lat}, ${hub.department__lon}
                                `);

                                polygon.hubId = hub.id;
                                drawnItems.addLayer(polygon);
                                loadedCount++;
                            } catch (e) {
                                console.error('Ошибка при загрузке полигона:', e);
                            }
                        }
                    });

                    if (loadedCount > 0) {
                        map.fitBounds(drawnItems.getBounds());
                    }

                    hasChanges = false;
                    updateButtonStates();
                    $('#hubs-status').html(`<strong>Статус:</strong> Загружено ${loadedCount} хабов`);
                },
                error: function() {
                    $('#hubs-status').html('<strong>Статус:</strong> Ошибка при загрузке хабов');
                }
            });
        }

        function saveHubs() {
            if (!hasChanges) {
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Сохранение изменений...');

            const updates = [];

            drawnItems.eachLayer(function(layer) {
                if (layer.hubId) {
                    const geoJson = layer.toGeoJSON();
                    updates.push({
                        id: layer.hubId,
                        polygon: JSON.stringify(geoJson.geometry)
                    });
                }
            });

            // Здесь должен быть AJAX запрос для сохранения
            // Пока что просто имитируем успешное сохранение
            setTimeout(function() {
                originalHubsData = JSON.parse(JSON.stringify(hubsData));
                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Изменения сохранены успешно');
            }, 1000);
        }

        function resetHubs() {
            if (!hasChanges) {
                return;
            }

            if (!confirm('Вы уверены, что хотите отменить все изменения?')) {
                return;
            }

            // Восстановление исходного состояния
            drawnItems.clearLayers();

            originalHubsData.forEach(function(hub) {
                if (hub.polygon) {
                    try {
                        const polygon = L.geoJSON(JSON.parse(hub.polygon), {
                            style: function(feature) {
                                return {
                                    color: hub.department__status === 1 ? '#0066cc' : '#cc0000',
                                    weight: 2,
                                    opacity: 0.8,
                                    fillOpacity: 0.3
                                };
                            }
                        });

                        polygon.bindPopup(`
                            <strong>${hub.department__name}</strong><br>
                            Статус: ${hub.department__status === 1 ? 'Активное' : 'Неактивное'}<br>
                            Координаты: ${hub.department__lat}, ${hub.department__lon}
                        `);

                        polygon.hubId = hub.id;
                        drawnItems.addLayer(polygon);
                    } catch (e) {
                        console.error('Ошибка при восстановлении полигона:', e);
                    }
                }
            });

            hasChanges = false;
            updateButtonStates();
            $('#hubs-status').html('<strong>Статус:</strong> Изменения отменены');
        }

        // Обработчики событий
        $('#layer-select, #region-select').on('change', updateButtonStates);
        $('#load-hubs-button').on('click', loadHubs);
        $('#save-hubs-button').on('click', saveHubs);
        $('#reset-hubs-button').on('click', resetHubs);

        // Инициализация
        initMap();
        updateButtonStates();
    });
</script>
{% endblock %}
