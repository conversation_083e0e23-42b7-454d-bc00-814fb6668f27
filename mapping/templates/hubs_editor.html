{% load static %}

<!DOCTYPE html>
<html>
<head>
    <title>{{ page_title }} - КазПочта</title>
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
    <link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">
    <link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">

    <!-- Дополнительные стили для панели инструментов -->
    <style>
        .leaflet-draw-toolbar {
            display: block !important;
            visibility: visible !important;
        }

        .leaflet-draw {
            display: block !important;
        }

        .leaflet-control-draw {
            display: block !important;
        }

        /* Принудительно показываем панель */
        .leaflet-top.leaflet-left {
            display: block !important;
        }
    </style>

    <script type="text/javascript" src="{% static 'map/js/leaflet.js' %}"></script>
    <script type="text/javascript" src="{% static 'map/js/jquery-3.7.0.min.js' %}"></script>
    <script src="{% static 'map/js/leaflet.draw.js' %}"></script>
    <script src="{% static 'map/js/turf.min.js' %}"></script>
    <script src="{% static 'map/js/Control.Geocoder.js' %}"></script>
    <script src="{% static 'map/js/bootstrap.min.js' %}"></script>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Helvetica Neue", Arial, sans-serif;
            margin: 0;
        }

        .page-header {
            background-color: #417690;
            color: white;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .page-header h1 {
            margin: 0;
            font-size: 24px;
        }

        .page-header .breadcrumb {
            margin: 5px 0 0 0;
            font-size: 14px;
        }

        .page-header .breadcrumb a {
            color: #cce7f0;
            text-decoration: none;
        }

        .page-header .breadcrumb a:hover {
            color: white;
        }

        #content-main {
            display: flex;
            flex-direction: row;
            gap: 20px;
            padding: 0 20px;
        }

        /* Для админки - меньше отступов */
        .admin-mode #content-main {
            padding: 0 10px;
        }

        .admin-mode #content-related {
            padding: 15px;
        }

        #content-related {
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            padding: 20px;
            flex: 1;
        }

        .controls-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .controls-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 16px;
        }

        #hubs-form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        #hubs-form label {
            font-weight: bold;
            margin-right: 5px;
        }

        #layer-select, #region-select {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-width: 150px;
        }

        .btn {
            padding: 8px 15px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #417690;
            color: white;
        }

        .btn-primary:hover {
            background-color: #205067;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #1e7e34;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
        }

        #map {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .status-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
        }

        .status-info strong {
            color: #495057;
        }

        .help-text {
            margin-top: 10px;
            font-size: 13px;
            color: #6c757d;
        }

        .help-text ul {
            margin: 5px 0;
            padding-left: 20px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body{% if request.resolver_match.url_name == 'hubs_editor_admin' %} class="admin-mode"{% endif %}>
{% if not request.resolver_match.url_name == 'hubs_editor_admin' %}
{% include "header.html" %}

<div class="page-header">
    <h1>{{ page_title }}</h1>
    <div class="breadcrumb">
        <a href="{% url 'layer_map' %}">← Вернуться к карте</a>
        {% if user.is_superuser %}
        | <a href="{% url 'admin:index' %}">Админка</a>
        {% endif %}
    </div>
</div>
{% else %}
<!-- Админка - минимальный header -->
<div style="background-color: #417690; color: white; padding: 10px 20px; margin-bottom: 20px;">
    <h1 style="margin: 0; font-size: 20px;">{{ page_title|default:"Групповое редактирование хабов" }}</h1>
    <div style="font-size: 14px; margin-top: 5px;">
        <a href="{% url 'admin:index' %}" style="color: #cce7f0; text-decoration: none;">← Вернуться в админку</a>
    </div>
</div>
{% endif %}

<div id="content-main">
    <div id="content-related">
        {% csrf_token %}
        
        <div class="controls-section">
            <h3>Параметры загрузки</h3>
            <form id="hubs-form">
                <label for="layer-select">Слой:</label>
                <select id="layer-select">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>

                <label for="region-select">Регион:</label>
                <select id="region-select">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>

                <div class="checkbox-container">
                    <input type="checkbox" id="show-polygons" name="show-polygons">
                    <label for="show-polygons">Только полигоны</label>
                </div>

                <button id="load-hubs-button" type="button" class="btn btn-primary">Загрузить хабы</button>
            </form>
        </div>

        <div class="controls-section">
            <h3>Действия с хабами</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button id="save-hubs-button" type="button" class="btn btn-success" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn btn-warning" disabled>Отменить изменения</button>
            </div>
            
            <div class="help-text">
                <strong>Инструкция:</strong>
                <ul>
                    <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                    <li>Используйте инструменты редактирования для изменения полигонов</li>
                    <li>Нажмите "Сохранить изменения" для применения</li>
                    <li>Синие полигоны - активные отделения, красные - неактивные</li>
                </ul>
            </div>
        </div>

        <div id="map"></div>
        
        <div class="status-info">
            <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        var map = null;
        var drawnItems = null;
        var hubsData = [];
        var departments = [];
        var originalHubsData = []; // Для отмены изменений
        var hasChanges = false;

        // Инициализация карты
        function initMap() {
            console.log('Initializing map...');
            console.log('Leaflet version:', L.version);
            console.log('Leaflet.Draw available:', typeof L.Control.Draw !== 'undefined');

            if (map) {
                map.remove();
            }

            map = L.map('map').setView([43.257109, 76.946314], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            // Проверяем что Leaflet.Draw загружен
            if (typeof L.Control.Draw !== 'undefined') {
                console.log('Creating draw control...');

                var drawControl = new L.Control.Draw({
                    draw: {
                        polygon: {
                            allowIntersection: false,
                            showArea: true
                        },
                        polyline: false,
                        rectangle: false,
                        circle: false,
                        marker: false,
                        circlemarker: false
                    },
                    edit: {
                        featureGroup: drawnItems,
                        edit: true,
                        remove: true
                    }
                });

                map.addControl(drawControl);
                console.log('Draw control added to map');

                // Проверяем что панель создалась
                setTimeout(function() {
                    var toolbar = document.querySelector('.leaflet-draw-toolbar');
                    console.log('Toolbar element:', toolbar);
                    if (toolbar) {
                        console.log('Toolbar found, making visible');
                        toolbar.style.display = 'block';
                        toolbar.style.visibility = 'visible';
                    } else {
                        console.error('Toolbar not found!');
                    }
                }, 200);

            } else {
                console.error('Leaflet.Draw not loaded!');
            }

            // Обработчик изменений
            map.on(L.Draw.Event.EDITED, function (event) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span>');
            });
        }

        // Обновление состояния кнопок
        function updateButtonStates() {
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        // Загрузка отделений для региона
        function loadDepartmentsForRegion(selectedRegion, callback) {
            $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
                departments = data;
                if (callback) callback();
            });
        }

        // Отображение отделений на карте
        function displayDepartments() {
            departments.forEach(function (point) {
                var marker = L.marker([point.lat, point.lon]);
                marker.bindPopup(point.name);
                marker.addTo(map);
            });
        }

        // Загрузка хабов
        function loadHubs() {
            var selectedLayer = $('#layer-select').val();
            var selectedRegion = $('#region-select').val();
            var showPolygonsOnly = $('#show-polygons').is(':checked');

            if (!selectedLayer || !selectedRegion) {
                alert('Выберите слой и регион');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Загрузка...');

            // Очистка карты
            map.eachLayer(function (layer) {
                if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                    map.removeLayer(layer);
                }
            });
            drawnItems.clearLayers();

            // Загрузка отделений
            if (!showPolygonsOnly) {
                loadDepartmentsForRegion(selectedRegion, displayDepartments);
            }

            // Загрузка полигонов хабов
            var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

            $.getJSON(url, function (data) {
                hubsData = data.polygons;
                originalHubsData = JSON.parse(JSON.stringify(data.polygons)); // Глубокая копия

                data.polygons.forEach(function (polygon) {
                    var coords = polygon.geometry.coordinates[0];
                    var latLngs = coords.map(function (coord) {
                        return [coord[1], coord[0]];
                    });

                    var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                    var polygonLayer = L.polygon(latLngs, {
                        fillColor: fillColor,
                        interactive: true,
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.3
                    });

                    polygonLayer.hubId = polygon.id;
                    polygonLayer.originalData = polygon;

                    polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                        permanent: false,
                        direction: 'auto',
                    });

                    drawnItems.addLayer(polygonLayer);
                });

                if (data.region_lat && data.region_lon) {
                    map.setView([data.region_lat, data.region_lon], 10);
                }

                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Загружено хабов: ' + data.polygons.length);
            }).fail(function() {
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: red;">Ошибка загрузки</span>');
            });
        }

        // Сохранение изменений
        function saveHubs() {
            if (!hasChanges) {
                alert('Нет изменений для сохранения');
                return;
            }

            var changedHubs = [];

            drawnItems.eachLayer(function(layer) {
                if (layer instanceof L.Polygon && layer.hubId) {
                    var geoJson = layer.toGeoJSON();
                    changedHubs.push({
                        id: layer.hubId,
                        geometry: geoJson.geometry
                    });
                }
            });

            if (changedHubs.length === 0) {
                alert('Нет полигонов для сохранения');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Сохранение...');

            // TODO: Здесь будет AJAX запрос для массового сохранения
            console.log('Сохраняем хабы:', changedHubs);

            // Временная имитация сохранения
            setTimeout(function() {
                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: green;">Сохранено хабов: ' + changedHubs.length + '</span>');
                alert('Изменения сохранены! (пока только в консоли)');
            }, 1000);
        }

        // Отмена изменений
        function resetHubs() {
            if (!hasChanges) {
                return;
            }

            if (!confirm('Отменить все несохраненные изменения?')) {
                return;
            }

            // Очистка и перезагрузка оригинальных данных
            drawnItems.clearLayers();

            originalHubsData.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                var polygonLayer = L.polygon(latLngs, {
                    fillColor: fillColor,
                    interactive: true,
                    weight: 2,
                    opacity: 0.8,
                    fillOpacity: 0.3
                });

                polygonLayer.hubId = polygon.id;
                polygonLayer.originalData = polygon;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });

                drawnItems.addLayer(polygonLayer);
            });

            hasChanges = false;
            updateButtonStates();
            $('#hubs-status').html('<strong>Статус:</strong> Изменения отменены');
        }

        // Обработчики событий
        $('#load-hubs-button').on('click', loadHubs);
        $('#save-hubs-button').on('click', saveHubs);
        $('#reset-hubs-button').on('click', resetHubs);

        // Инициализация
        initMap();
    });
</script>

</body>
</html>
