{% load static %}

<!DOCTYPE html>
<html>
<head>
    <title>Карта полигонов отделений КазПочты</title>
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
    <link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">
    <link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">

    <script type="text/javascript" src="{% static 'map/js/leaflet.js' %}"></script>
    <script type="text/javascript" src="{% static 'map/js/jquery-3.7.0.min.js' %}"></script>
    <script src="{% static 'map/js/leaflet.draw.js' %}"></script>
    <script src="{% static 'map/js/turf.min.js' %}"></script>
    <script src="{% static 'map/js/Control.Geocoder.js' %}"></script>
    <script src="{% static 'map/js/bootstrap.min.js' %}"></script>


    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Helvetica Neue", Arial, sans-serif;
            margin: 0; /* Убрать отступы по умолчанию */
        }

        #content-main {
            display: flex;
            flex-direction: row;
        }

        #content-related {
            padding: 20px;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            margin: 10px 20px 20px 20px;
            flex: 1; /* Занимать всю доступную ширину */
        }

        #layer-form {
            margin-bottom: 20px;
        }

        #layer-select {
            padding: 8px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-right: 20px;
        }

        #region-select {
            padding: 8px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-right: 20px;
        }


        #submit-button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #map {
            height: 70vh;
        }
    </style>
</head>
<body>
{% include "header.html" %}
<div id="content-main">
   
    <div id="content-related">
        {% csrf_token %}
        <!-- Форма выбора слоя -->
        <form id="layer-form">
            <label for="layer-select">Выберите слой:</label>
            <select id="layer-select">
                {% for layer in layers %}
                <option value="{{ layer.id }}">{{ layer.name }}</option>
                {% endfor %}
            </select>

            <!-- Селект для выбора региона -->
            <label for="region-select">Выберите регион:</label>
            <select id="region-select">
                {% for region in regions %}
                <option value="{{ region.id }}">{{ region.name }}</option>
                {% endfor %}
            </select>
            <label for="show-polygons">Только полигоны:</label>
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <button id="submit-button" type="submit">Показать полигоны</button>
        </form>

        <!-- Карта для отображения полигонов -->
        <div id="map" style="height: 70vh;"></div>


        <!-- Скрипт для обработки выбора слоя и отображения полигонов -->
        <script>
            function setLayerCookie(layerId) {
                document.cookie = `selectedLayer=${layerId}`;
            }

            // Function to get the selected layer ID from the cookie
            function getSelectedLayerFromCookie() {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.startsWith('selectedLayer=')) {
                        return cookie.substring('selectedLayer='.length);
                    }
                }
                return null;
            }


            $(document).ready(function () {
                // Retrieve the selected layer from the cookie
                const selectedLayerFromCookie = getSelectedLayerFromCookie();

                // Set the selected layer in the dropdown if it exists in the cookie
                if (selectedLayerFromCookie) {
                    $('#layer-select').val(selectedLayerFromCookie);
                }

                //var map = L.map('map').setView([43.257109, 76.946314], 12);
                var map = L.map('map').setView([{{regions.0.lat }}, {{regions.0.lon}}], 12);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
                }).addTo(map);
                var departments = null;


                function loadDepartmentsForRegion(selectedRegion, callback) {
                    $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
                        if (data.length > 0) {
                            var firstDepartment = data[0];
                            console.log('firstDepartment: ', firstDepartment)
                            var lat = firstDepartment.lat;
                            var lon = firstDepartment.lon;
                            console.log('lat: ', firstDepartment.lat)
                            //map.setView([lat, lon], 12); // 12 - это уровень масштаба
                        }
                        departments = data;
                        callback();
                    });
                }

                // Функция для отображения отделений на карте
                function displayDepartments() {
                    departments.forEach(function (point) {
                        var lat = point.lat;
                        var lon = point.lon;
                        var name = point.name;


                        // Создание маркера для точки
                        var marker = L.marker([lat, lon]);

                        // Добавление всплывающей подсказки с именем точки
                        marker.bindPopup(name);

                        marker.addTo(map);
                    });
                }

                function showSaveConfirmation(layer, selectedDepartmentId) {
                    var departmentInfo = "";

                    if (selectedDepartmentId !== null) {
                        // Получить информацию о выбранном отделении на основе selectedDepartmentId
                        var selectedDepartment = departments.find(d => d.id === selectedDepartmentId);
                        if (selectedDepartment) {
                            departmentInfo = "Выбрано отделение: " + selectedDepartment.name;
                        }
                    }
                    console.log("layer", layer)
                    var confirmSave = confirm('Вы уверены, что хотите сохранить этот полигон?\n' + departmentInfo);

                    if (confirmSave) {
                        savePolygon(layer, selectedDepartmentId);
                    } else {
                        drawnItems.removeLayer(layer);
                        selectedPolygon = null;
                    }
                }

                // Загрузка данных об отделениях и отображение их на карте


                var drawnItems = new L.FeatureGroup();
                map.addLayer(drawnItems);

                var drawControl = new L.Control.Draw({
                    draw: {
                        polygon: true,
                        polyline: false,
                        rectangle: false,
                        circle: false,
                        marker: false,
                    },
                    edit: {
                        featureGroup: drawnItems,
                        edit: true,
                    },
                });
                map.addControl(drawControl);
                map.attributionControl.setPrefix('Kazpost Digital, 2024');


                function savePolygon(layer, selectedDepartmentId) {
                    // Code to save the polygon to the server
                    var selectedLayer = $('#layer-select').val();

                    if (selectedDepartmentId === null) {
                        alert('Отделение не выбрано.');
                        return false;
                    }

                    var polygonData = {
                        geometry: layer.toGeoJSON().geometry,
                        layer: selectedLayer,
                        department: selectedDepartmentId, // Теперь передаем единственный ID отделения
                    };

                    $.ajax({
                        url: '{{ KAZPOSTGEO_PREFIX }}/save_polygon/',
                        type: 'POST',
                        data: JSON.stringify(polygonData),
                        contentType: 'application/json; charset=utf-8',
                        headers: {
                            'X-CSRFToken': $('[name="csrfmiddlewaretoken"]').val()
                        },
                        success: function (data) {
                            layer.bindTooltip(selectedDepartmentId, {
                                    permanent: false,
                                    direction: 'auto'
                                });  // Всплывающая подсказка

                                layer.on('mouseover', function () {
                                    if (selectedPolygon !== layer) {
                                        layer.setStyle({fillColor: 'green'});  // Изменение цвета при наведении
                                    }
                                });

                                layer.on('mouseout', function () {
                                    if (selectedPolygon !== layer) {

                                        layer.setStyle({fillColor: 'red'}); // Или любой другой цвет для статуса, отличного от 1

                                        // Возвращение исходного цвета
                                    }
                                });
                            map.addLayer(layer);
                            // Handle success
                        },
                    });


                    // Удаляем из группы редактируемых полигонов
                    drawnItems.removeLayer(layer);
                    selectedPolygon = null;
                }


                // Обработка рисования полигонов
                map.on(L.Draw.Event.CREATED, function (event) {
                    var layer = event.layer;
                    drawnItems.addLayer(layer);

                    var selectedLayer = $('#layer-select').val();
                    var selectedDepartments = [];
                    departments.forEach(function (department) {
                        var lat = department.lat;
                        var lon = department.lon;
                        var departmentId = department.id;
                        var point = turf.point([lon, lat]);

                        if (turf.booleanPointInPolygon(point, layer.toGeoJSON())) {
                            selectedDepartments.push(departmentId);
                        }
                    });

                    if (selectedDepartments.length < 1) {
                        alert('Отделение не выбрано.');
                        drawnItems.removeLayer(layer);
                        return false;
                    }

                    // Если есть несколько отделений внутри полигона, показываем выбор отделения
                    if (selectedDepartments.length > 1) {
                        var select = document.createElement('select');
                        select.id = 'selected-department';

                        selectedDepartments.forEach(function (departmentId) {
                            var department = departments.find(d => d.id === departmentId);
                            var option = new Option(department.name, department.id);
                            select.appendChild(option);
                        });

                        var label = document.createElement('label');
                        label.htmlFor = 'selected-department';
                        label.innerText = 'Выберите отделение:';

                        var modalBody = document.createElement('div');
                        modalBody.appendChild(label);
                        modalBody.appendChild(select);

                        var confirmButton = document.createElement('button');
                        confirmButton.id = 'confirm-selection';
                        confirmButton.innerText = 'Подтвердить выбор';

                        // Show a Bootstrap Modal with department selection
                        var modal = document.createElement('div');
                        modal.classList.add('modal', 'fade');
                        modal.innerHTML = `
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Выбор отделения</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body"></div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Закрыть</button>
                                    <button type="button" class="btn btn-primary" id="confirm-selection">Подтвердить выбор</button>
                                </div>
                            </div>
                        </div>
                    `;

                        modal.querySelector('.modal-body').appendChild(modalBody);

                        document.body.appendChild(modal);

                        $(modal).modal('show');

                        modal.querySelector('#confirm-selection').addEventListener('click', function () {
                            var selectedDepartmentId = select.value;
                            savePolygon(layer, selectedDepartmentId);
                            $(modal).modal('hide');
                        });
                    } else {
                        console.log("selectedDepartments[0]", selectedDepartments[0])
                        // If there's only one department, save the polygon directly
                        showSaveConfirmation(layer, selectedDepartments[0]);
                    }
                });
                var selectedPolygon = null;  // Переменная для хранения выбранного полигона

                $('#layer-form').on('submit', function (e) {
                    e.preventDefault();

                    var selectedLayer = $('#layer-select').val();
                    const selectedRegion = $('#region-select').val();
                    const showPolygonsOnly = $('#show-polygons').is(':checked');
                    if (selectedLayer) {
                        // Очистка карты от предыдущих полигонов и маркеров
                        map.eachLayer(function (layer) {
                            if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                                map.removeLayer(layer);
                            }
                        });

                        // Загрузка данных об отделениях и отображение их на карте
                        if (!showPolygonsOnly){
                            loadDepartmentsForRegion(selectedRegion, displayDepartments);
                        }


                        // Загрузка и отображение полигонов, связанных с выбранным слоем
                        var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

                        $.getJSON(url, function (data) {
                            console.log("полигоны по слою", data)
                            data.polygons.forEach(function (polygon) {
                                var coords = polygon.geometry.coordinates[0];
                                var latLngs = coords.map(function (coord) {
                                    return [coord[1], coord[0]];
                                });

                                var fillColor = 'blue'; // Цвет по умолчанию
                                // Изменение цвета в зависимости от статуса
                                if (polygon.status !== 1) {
                                    fillColor = 'red'; // Или любой другой цвет для статуса, отличного от 1
                                }

                                var polygonLayer = L.polygon(latLngs, {fillColor: fillColor,
                                    interactive: true,
                                });

                                polygonLayer.bindTooltip(polygon.departament, {
                                    permanent: false,
                                    direction: 'auto',
                                });  // Всплывающая подсказка

                                polygonLayer.on('mouseover', function () {
                                    if (selectedPolygon !== polygonLayer) {
                                        polygonLayer.setStyle({fillColor: 'green'});  // Изменение цвета при наведении
                                    }
                                });

                                polygonLayer.on('mouseout', function () {
                                    if (selectedPolygon !== polygonLayer) {
                                        if (polygon.status !== 1) {
                                            polygonLayer.setStyle({fillColor: 'red'}); // Или любой другой цвет для статуса, отличного от 1
                                        } else {
                                            polygonLayer.setStyle({fillColor: 'blue'});
                                        }
                                        // Возвращение исходного цвета
                                    }
                                });

                                polygonLayer.bindPopup("Loading...")
                                urlcount = '{{ ENV_PREFIX }}/geobuildapi/v1/getcountPolygon'
                                polygonLayer.on('click', function () {
                                    if (selectedPolygon) {
                                        selectedPolygon.setStyle({fillColor: 'blue'});
                                    }
                                    selectedPolygon = polygonLayer;
                                    var popup =polygonLayer.getPopup();
                                    $.postJSON(urlcount, polygon.polygons, function(data, textStatus) {
                                        console.log(data)
                                        response=data
                                        console.log(response.count)
                                        popup.setContent("Количество зданий - "+response.count);
                                        popup.update();
                                    }, "json");

                                });
                                drawnItems.addLayer(polygonLayer);  // Добавляем в группу редактируемых полигонов

                                polygonLayer.addTo(map);

                                // Добавление маркера для координат департамента
                                var departmentMarker = L.marker([polygon.lat, polygon.lon]).addTo(map);
                                departmentMarker.bindPopup(polygon.departament).openPopup();
                            });
                            console.log("region_lat ",data.region_lat)
                            map.setView([data.region_lat, data.region_lon], 12)
                        });
                    }
                });
                $('#layer-form').on('submit', function (e) {
                    e.preventDefault();
                    const selectedLayer = $('#layer-select').val();

                    // Store the selected layer in a cookie
                    setLayerCookie(selectedLayer);

                    // ... (your existing code) ...
                });

            });
            $.postJSON = function(url, data, callback) {
                    return jQuery.ajax({
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    'type': 'POST',
                    'url': url,
                    'data': JSON.stringify(data),
                    'dataType': 'json',
                    'success': callback
                    });
                };
        </script>


</body>
</html>
