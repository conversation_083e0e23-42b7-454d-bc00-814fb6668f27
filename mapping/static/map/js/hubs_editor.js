console.log('=== HUBS EDITOR JS LOADED ===');
alert('HUBS EDITOR JS ФАЙЛ ЗАГРУЖЕН!');

// Проверяем загрузку зависимостей
console.log('jQuery loaded:', typeof $ !== 'undefined');
console.log('django object:', typeof django !== 'undefined');
console.log('Leaflet loaded:', typeof L !== 'undefined');

// Функция для инициализации с разными вариантами jQuery
function initHubsEditor($) {
    console.log('=== INIT HUBS EDITOR ===');
    console.log('jQuery version:', $.fn ? $.fn.jquery : 'no version');
    
    $(document).ready(function() {
        console.log('=== JQUERY READY FIRED ===');
        
        var map = null;
        var drawnItems = null;
        var hubsData = [];
        var departments = [];
        var originalHubsData = [];
        var hasChanges = false;

        // Инициализация карты
        function initMap() {
            console.log('=== INITIALIZING MAP ===');
            console.log('Leaflet version:', typeof L !== 'undefined' ? L.version : 'Leaflet not loaded');
            
            var mapElement = document.getElementById('map');
            console.log('Map element:', mapElement);
            console.log('Map element dimensions:', mapElement ? mapElement.offsetWidth + 'x' + mapElement.offsetHeight : 'not found');
            
            if (map) {
                map.remove();
            }

            try {
                map = L.map('map').setView([43.257109, 76.946314], 6);
                console.log('Map created successfully');
            } catch (error) {
                console.error('Error creating map:', error);
                return;
            }
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            // Проверяем что Leaflet.Draw загружен
            if (typeof L.Control.Draw !== 'undefined') {
                console.log('Creating draw control...');

                var drawControl = new L.Control.Draw({
                    draw: {
                        polygon: {
                            allowIntersection: false,
                            showArea: true
                        },
                        polyline: false,
                        rectangle: false,
                        circle: false,
                        marker: false,
                        circlemarker: false
                    },
                    edit: {
                        featureGroup: drawnItems,
                        edit: true,
                        remove: true
                    }
                });

                map.addControl(drawControl);
                console.log('Draw control added to map');

                // Принудительно показываем панель инструментов
                setTimeout(function() {
                    var toolbar = document.querySelector('.leaflet-draw-toolbar');
                    if (toolbar) {
                        toolbar.style.display = 'block';
                        toolbar.style.visibility = 'visible';
                    }
                }, 200);

            } else {
                console.error('Leaflet.Draw not loaded!');
            }

            // Обработчик изменений
            map.on(L.Draw.Event.EDITED, function (event) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span>');
            });
        }

        // Обновление состояния кнопок
        function updateButtonStates() {
            var layerSelected = $('#layer-select').val();
            var regionSelected = $('#region-select').val();

            $('#load-hubs-button').prop('disabled', !layerSelected || !regionSelected);
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        // Простые обработчики для тестирования
        $('#layer-select, #region-select').on('change', function() {
            console.log('Select changed');
            updateButtonStates();
        });
        
        $('#load-hubs-button').on('click', function() {
            console.log('Load hubs clicked');
        });

        // Инициализация с задержкой
        setTimeout(function() {
            console.log('=== STARTING INIT ===');
            initMap();
            updateButtonStates();
        }, 100);
    });
}

// Пробуем разные варианты jQuery
if (typeof django !== 'undefined' && django.jQuery) {
    console.log('Using django.jQuery');
    initHubsEditor(django.jQuery);
} else if (typeof jQuery !== 'undefined') {
    console.log('Using global jQuery');
    initHubsEditor(jQuery);
} else if (typeof $ !== 'undefined') {
    console.log('Using global $');
    initHubsEditor($);
} else {
    console.error('No jQuery found!');
}
