"""
Локальные настройки для разработки без VPN
"""
from .settings import *

# Переопределяем настройки базы данных для локальной разработки
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.spatialite',
        'NAME': BASE_DIR / 'db_local.sqlite3',
    }
}

# Отключаем проверки безопасности для локальной разработки
DEBUG = True
ALLOWED_HOSTS = ['*']

# Можно отключить некоторые middleware для упрощения
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # Временно отключаем changelog middleware если он вызывает проблемы
    # 'changelog.middleware.LoggedInUserMiddleware'
]

print("Используются локальные настройки для разработки")
