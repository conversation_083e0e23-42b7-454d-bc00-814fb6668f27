.leaflet-control-minimap {
    border:solid rgba(255, 255, 255, 1.0) 4px;
    box-shadow: 0 1px 5px rgba(0,0,0,0.65);
    border-radius: 3px;
    background: #f8f8f9;
    transition: all .2s;
}

.leaflet-control-minimap a {
    background-color: rgba(255, 255, 255, 1.0);
    background-repeat: no-repeat;
    z-index: 99999;
    transition: all .2s;
    border-radius: 3px 0px 0px 0px;
}

.leaflet-control-minimap a.minimized {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    border-radius: 0px;
}

.leaflet-control-minimap-toggle-display {
    background-image: url("images/toggle.png");
    height: 19px;
    width: 19px;
    position: absolute;
    bottom: 0;
    right: 0; 
}
