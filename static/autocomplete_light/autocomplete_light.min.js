var yl=yl||{};yl.functions=yl.functions||{},yl.registerFunction=function(e,t){if(this.functions.hasOwnProperty(e))console.error('The DAL function "'+e+'" has already been registered.');else{if("function"!=typeof t)throw new Error("The custom DAL function must be a function.");this.functions[e]=t;t=new CustomEvent("dal-function-registered."+e,{detail:{name:e,func:t}});window.dispatchEvent(t)}},window.addEventListener("load",function(){window.django=window.django||{},django.hasOwnProperty("jQuery")||"undefined"===jQuery||(django.jQuery=jQuery),function($){$.fn.getFormPrefix=function(){var e,t=$(this).attr("name").split("-");for(e in t){var n=t.slice(0,-e).join("-");if(n.length)if($(":input[name^="+(n+="-")+"]").length)return n}return""},$.fn.getFormPrefixes=function(){var e=$(this).attr("name").split("-").slice(0,-1),t=[];for(i=0;i<e.length;i+=2){var n=e.slice(0,-i||e.length).join("-");n.length&&$(":input[name^="+(n+="-")+"]").length&&t.push(n)}return t.push(""),t},"undefined"!=typeof dalLoadLanguage?dalLoadLanguage($):document.addEventListener("dal-language-loaded",function(e){dalLoadLanguage($)});var e=new CustomEvent("dal-init-function");document.dispatchEvent(e);var r=[];function o(t){var n,e;void 0!==t&&"number"!=typeof t||(t=this),0<=r.indexOf(t)||(n=$(t).attr("data-autocomplete-light-function"),yl.functions.hasOwnProperty(n)&&"function"==typeof yl.functions[n]?yl.functions[n]($,t):yl.functions.hasOwnProperty(n)?window.addEventListener("dal-function-registered."+n,function(e){yl.functions[n]($,t)}):console.warn('Your custom DAL function "'+n+'" uses a deprecated event listener that will be removed in future versions. https://django-autocomplete-light.readthedocs.io/en/master/tutorial.html#overriding-javascript-code'),$(t).trigger("autocompleteLightInitialize"),r.push(t),e=new CustomEvent("dal-element-initialized",{detail:{element:t}}),document.dispatchEvent(e))}function a(e){return"classList"in e&&e.classList.contains("ui-sortable-helper")}$.fn.excludeTemplateForms=function(){return this.not("[id*=__prefix__]").filter(function(){return!this.id.match(/-empty-/)||this.id.match(/-\d+-empty-\d+-/)})},window.__dal__initialize||(window.__dal__initialize=o,$(document).ready(function(){$("[data-autocomplete-light-function]").excludeTemplateForms().each(o)}),"MutationObserver"in window?new MutationObserver(function(e){for(var t,n,r=0;r<e.length;r++)if(0<(t=e[r]).addedNodes.length)for(var i=0;i<t.addedNodes.length;i++){if(a(n=t.addedNodes[i]))return;$(n).find("[data-autocomplete-light-function]").excludeTemplateForms().each(o)}}).observe(document.documentElement,{childList:!0,subtree:!0}):$(document).on("DOMNodeInserted",function(e){a(e.target)||$(e.target).find("[data-autocomplete-light-function]").excludeTemplateForms().each(o)})),document.csrftoken=function(e){var t=null;if(document.cookie&&""!=document.cookie)for(var n=document.cookie.split(";"),r=0;r<n.length;r++){var i=$.trim(n[r]);if(i.substring(0,e.length+1)==e+"="){t=decodeURIComponent(i.substring(e.length+1));break}}return t}("csrftoken"),null!==document.csrftoken||0<(e=$('form :input[name="csrfmiddlewaretoken"]')).length&&(document.csrftoken=e[0].value)}(django.jQuery),function($){"use strict";$.fn.djangoAdminSelect2=function(e){var n=$.extend({},e);return $.each(this,function(e,t){!function(t,e){e=$.extend({ajax:{data:function(e){return{term:e.term,page:e.page,app_label:t.data("app-label"),model_name:t.data("model-name"),field_name:t.data("field-name")}}}},e);t.select2(e)}($(t),n)}),this},$(function(){$(".admin-autocomplete").not("[name*=__prefix__]").djangoAdminSelect2()}),$(document).on("formset:added",function(e,t){return t.find(".admin-autocomplete").djangoAdminSelect2()})}(django.jQuery),function($,yl){function i(e){var n;return 1===e.length&&"checkbox"===e.attr("type")&&void 0===e.attr("value")?"exists":1===e.length&&void 0!==e.attr("multiple")?"multiple":(n=!0,$.each(e,function(e,t){"checkbox"!==$(t).attr("type")&&(n=!1)}),n?"multiple":"single")}yl.forwardHandlerRegistry=yl.forwardHandlerRegistry||{},yl.registerForwardHandler=function(e,t){yl.forwardHandlerRegistry[e]=t},yl.getForwardHandler=function(e){return yl.forwardHandlerRegistry[e]},yl.getFieldRelativeTo=function(e,t){for(var n=$(e).getFormPrefixes(),r=0;r<n.length;r++){var i="[name="+n[r]+t+"]",i=$(i);if(i.length)return i}return $()},yl.getValueFromField=function(e){var t=i(e),n=$(e).serializeArray();0==n&&$(e).prop("disabled")&&($(e).prop("disabled",!1),n=$(e).serializeArray(),$(e).prop("disabled",!0));function r(e){return e.hasOwnProperty("value")&&void 0!==e.value?e.value:null}e=function(e){e=e,e=n.length>e?n[e]:null;return null!==e?r(e):null};return"multiple"===t?n.map(r):"exists"===t?0<n.length:e(0)},yl.getForwards=function(i){var e,o,t="div.dal-forward-conf#dal-forward-conf-for-"+i.attr("id")+", div.dal-forward-conf#dal-forward-conf-for_"+i.attr("id"),t=(0<i.length?$(i[0].form):$()).find(t).find("script");if(0!==t.length){try{e=JSON.parse(t.text())}catch(e){return}if(Array.isArray(e))return o={},$.each(e,function(e,t){var n,r;"const"===t.type?o[t.dst]=t.val:"self"===t.type?(n=t.hasOwnProperty("dst")?t.dst:"self",o[n]=yl.getValueFromField(i)):"field"===t.type?(r=t.src,n=t.hasOwnProperty("dst")?t.dst:r,(r=yl.getFieldRelativeTo(i,r)).length&&(o[n]=yl.getValueFromField(r))):"javascript"===t.type&&(r=yl.getForwardHandler(t.handler),o[t.dst||t.handler]=r(i))}),JSON.stringify(o)}}}(django.jQuery,yl)});