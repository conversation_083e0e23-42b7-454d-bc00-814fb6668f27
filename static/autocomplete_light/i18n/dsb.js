/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
var dalLoadLanguage=function(n){var e;(e=n&&n.fn&&n.fn.select2&&n.fn.select2.amd?n.fn.select2.amd:e).define("select2/i18n/dsb",[],function(){function e(n,e){return 1===n?e[0]:2===n?e[1]:2<n&&n<=4?e[2]:5<=n?e[3]:void 0}var a=["znamu<PERSON><PERSON>","znamu<PERSON><PERSON>","znamu<PERSON><PERSON>","znamu<PERSON>kow"],u=["zapisk","zapiska","zapiski","zapiskow"];return{errorLoading:function(){return"Wuslědki njejsu se dali zacytaś."},inputTooLong:function(n){n=n.input.length-n.maximum;return"Pšosym lašuj "+n+" "+e(n,a)},inputTooShort:function(n){n=n.minimum-n.input.length;return"Pšosym zapódaj nanejmjenjej "+n+" "+e(n,a)},loadingMore:function(){return"Dalšne wuslědki se zacytaju…"},maximumSelected:function(n){return"Móžoš jano "+n.maximum+" "+e(n.maximum,u)+"wubraś."},noResults:function(){return"Žedne wuslědki namakane"},searching:function(){return"Pyta se…"},removeAllItems:function(){return"Remove all items"}}}),e.define,e.require},event=new CustomEvent("dal-language-loaded",{lang:"dsb"});document.dispatchEvent(event);