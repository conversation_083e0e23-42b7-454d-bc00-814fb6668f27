/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
var dalLoadLanguage=function(e){var n;(n=e&&e.fn&&e.fn.select2&&e.fn.select2.amd?e.fn.select2.amd:n).define("select2/i18n/cs",[],function(){function n(e,n){switch(e){case 2:return n?"dva":"dvě";case 3:return"tři";case 4:return"čtyři"}return""}return{errorLoading:function(){return"Výsledky nemohly být načteny."},inputTooLong:function(e){e=e.input.length-e.maximum;return 1==e?"Prosím, zadejte o jeden znak méně.":e<=4?"Prosím, zadejte o "+n(e,!0)+" znaky méně.":"Prosím, zadejte o "+e+" znak<PERSON> méně."},inputTooShort:function(e){e=e.minimum-e.input.length;return 1==e?"Prosím, zadejte je<PERSON>t<PERSON> jeden znak.":e<=4?"Prosím, zadejte ještě další "+n(e,!0)+" znaky.":"Prosím, zadejte ještě dalších "+e+" znaků."},loadingMore:function(){return"Načítají se další výsledky…"},maximumSelected:function(e){e=e.maximum;return 1==e?"Můžete zvolit jen jednu položku.":e<=4?"Můžete zvolit maximálně "+n(e,!1)+" položky.":"Můžete zvolit maximálně "+e+" položek."},noResults:function(){return"Nenalezeny žádné položky."},searching:function(){return"Vyhledávání…"},removeAllItems:function(){return"Odstraňte všechny položky"}}}),n.define,n.require},event=new CustomEvent("dal-language-loaded",{lang:"cs"});document.dispatchEvent(event);