document.addEventListener("dal-init-function",function(){yl.registerFunction("select2",function($,t){var n=$(t);function r(t,e){if(e){e=$("<span>");return e.html(t),e}return t}function e(t){var e=void 0!==n.attr("data-html")||void 0!==n.attr("data-result-html");if(t.create_id){var a=$("<span>").addClass("dal-create");return e?a.html(t.text):a.text(t.text)}return r(t.text,e)}t=null;n.attr("data-autocomplete-light-url")&&(t={url:n.attr("data-autocomplete-light-url"),dataType:"json",delay:250,data:function(t){return{q:t.term,page:t.page,create:n.attr("data-autocomplete-light-create")&&!n.attr("data-tags"),forward:yl.getForwards(n)}},processResults:function(t,e){return n.attr("data-tags")&&$.each(t.results,function(t,e){e.id=e.text}),t},cache:!0}),use_tags=!1,tokenSeparators=null,n.attr("data-tags")&&(tokenSeparators=[","],use_tags=!0),n.attr("data-token-separators")&&(use_tags=!0,tokenSeparators=n.attr("data-token-separators"),"null"==tokenSeparators&&(tokenSeparators=null)),n.select2({tokenSeparators:tokenSeparators,debug:!0,containerCssClass:":all:",placeholder:n.attr("data-placeholder")||"",language:n.attr("data-autocomplete-light-language"),minimumInputLength:n.attr("data-minimum-input-length")||0,allowClear:!n.is("[required]"),templateResult:e,templateSelection:function(t){return void 0!==t.selected_text?r(t.selected_text,void 0!==n.attr("data-html")||void 0!==n.attr("data-selected-html")):e(t)},ajax:t,with:null,tags:use_tags}),n.on("select2:selecting",function(t){var r,e=t.params.args.data;!0===e.create_id&&(t.preventDefault(),$.ajax({url:(r=n).attr("data-autocomplete-light-url"),type:"POST",dataType:"json",data:{text:e.id,forward:yl.getForwards(n)},beforeSend:function(t,e){t.setRequestHeader("X-CSRFToken",document.csrftoken)},success:function(t,e,a){"error"in t?(error=t.error,$(".dal-create").append(`<p class="invalid-feedback d-block""><strong>${error}</strong>`)):(r.append($("<option>",{value:t.id,text:t.text,selected:!0})),r.trigger("change"),r.select2("close"))}}))})})});